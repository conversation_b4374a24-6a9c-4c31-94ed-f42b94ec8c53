<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PageTalk 图标功能测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #eee;
            border-radius: 8px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        .icon-demo {
            display: flex;
            align-items: center;
            gap: 10px;
            margin: 10px 0;
            padding: 10px;
            background: #f9f9f9;
            border-radius: 6px;
        }
        .icon-demo svg {
            color: #666;
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .selected-text {
            background: #fff3cd;
            padding: 10px;
            border-radius: 6px;
            border: 1px solid #ffeaa7;
            margin: 10px 0;
            user-select: text;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>PageTalk 划词助手图标功能测试</h1>
        
        <div class="test-section">
            <h3>1. Lucide图标渲染测试</h3>
            <p>测试Lucide图标库是否正确加载和渲染：</p>
            <div id="icon-test-area">
                <!-- 图标将在这里动态生成 -->
            </div>
            <button class="test-button" onclick="testIconRendering()">测试图标渲染</button>
        </div>

        <div class="test-section">
            <h3>2. 划词助手功能测试</h3>
            <p>选择下面的文本来测试划词助手功能：</p>
            <div class="selected-text">
                这是一段测试文本，用于测试PageTalk划词助手的图标自定义功能。请选择这段文字来查看选项栏是否正确显示自定义图标。
            </div>
            <div class="selected-text">
                This is a test text for testing PageTalk's text selection helper icon customization feature. Please select this text to see if the options bar displays custom icons correctly.
            </div>
        </div>

        <div class="test-section">
            <h3>3. 图标选择器测试</h3>
            <p>测试图标选择器弹窗功能：</p>
            <button class="test-button" onclick="testIconPicker()">打开图标选择器</button>
            <div id="selected-icon-display" style="margin-top: 10px;">
                <span>当前选择的图标：</span>
                <span id="current-icon">star</span>
                <span id="current-icon-preview" style="margin-left: 10px;"></span>
            </div>
        </div>

        <div class="test-section">
            <h3>4. 测试结果</h3>
            <div id="test-results">
                <p>测试结果将在这里显示...</p>
            </div>
        </div>
    </div>

    <!-- 加载必要的库文件 -->
    <script src="js/lib/lucide.js"></script>
    <script src="js/translations.js"></script>
    <script src="js/text-selection-helper-settings.js"></script>
    <script src="js/text-selection-helper.js"></script>

    <script>
        // 测试图标渲染功能
        function testIconRendering() {
            const testArea = document.getElementById('icon-test-area');
            const testIcons = ['Star', 'Heart', 'Bookmark', 'Tag', 'Flag', 'Bell', 'Eye', 'Search'];

            testArea.innerHTML = '';

            testIcons.forEach(iconName => {
                const iconDemo = document.createElement('div');
                iconDemo.className = 'icon-demo';

                try {
                    // 直接使用Lucide库测试
                    let iconHtml = '';
                    if (typeof lucide !== 'undefined' && lucide[iconName]) {
                        const iconData = lucide[iconName];
                        if (iconData && Array.isArray(iconData)) {
                            let svgContent = '';
                            iconData.forEach(([tag, attrs]) => {
                                if (tag === 'path') {
                                    svgContent += `<path d="${attrs.d}"/>`;
                                } else if (tag === 'circle') {
                                    svgContent += `<circle cx="${attrs.cx}" cy="${attrs.cy}" r="${attrs.r}"/>`;
                                } else if (tag === 'rect') {
                                    svgContent += `<rect x="${attrs.x}" y="${attrs.y}" width="${attrs.width}" height="${attrs.height}"${attrs.rx ? ` rx="${attrs.rx}"` : ''}/>`;
                                } else if (tag === 'line') {
                                    svgContent += `<line x1="${attrs.x1}" y1="${attrs.y1}" x2="${attrs.x2}" y2="${attrs.y2}"/>`;
                                } else if (tag === 'polyline') {
                                    svgContent += `<polyline points="${attrs.points}"/>`;
                                }
                            });

                            iconHtml = `<svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round">
                                ${svgContent}
                            </svg>`;
                        }
                    }

                    if (!iconHtml) {
                        iconHtml = `<span style="color: red;">图标 ${iconName} 未找到</span>`;
                    }

                    iconDemo.innerHTML = `
                        ${iconHtml}
                        <span>${iconName}</span>
                    `;

                    testArea.appendChild(iconDemo);
                } catch (error) {
                    iconDemo.innerHTML = `<span style="color: red;">错误: ${error.message}</span>`;
                    testArea.appendChild(iconDemo);
                }
            });

            updateTestResults('图标渲染测试完成');
        }

        // 测试图标选择器
        function testIconPicker() {
            if (typeof showIconPicker === 'function') {
                showIconPicker((selectedIcon) => {
                    document.getElementById('current-icon').textContent = selectedIcon;
                    const preview = document.getElementById('current-icon-preview');
                    if (renderLucideIconForSettings) {
                        preview.innerHTML = renderLucideIconForSettings(selectedIcon, 20);
                    }
                    updateTestResults(`图标选择器测试完成，选择了图标: ${selectedIcon}`);
                }, 'star', {
                    selectIcon: '选择图标',
                    searchIcons: '搜索图标...'
                });
            } else {
                updateTestResults('错误: showIconPicker 函数未找到');
            }
        }

        // 更新测试结果
        function updateTestResults(message) {
            const results = document.getElementById('test-results');
            const timestamp = new Date().toLocaleTimeString();
            results.innerHTML += `<p>[${timestamp}] ${message}</p>`;
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            updateTestResults('页面加载完成，开始测试...');

            // 检查Lucide库是否加载
            if (typeof lucide !== 'undefined') {
                updateTestResults('✓ Lucide图标库加载成功');

                // 检查Lucide库的内容
                const iconNames = Object.keys(lucide).filter(key =>
                    typeof lucide[key] !== 'function' && Array.isArray(lucide[key])
                );
                updateTestResults(`✓ Lucide库包含 ${iconNames.length} 个图标`);

                // 测试几个常用图标
                const testIcons = ['Star', 'Heart', 'Home', 'User'];
                testIcons.forEach(iconName => {
                    if (lucide[iconName]) {
                        updateTestResults(`✓ 找到图标: ${iconName}`);
                    } else {
                        updateTestResults(`✗ 未找到图标: ${iconName}`);
                    }
                });
            } else {
                updateTestResults('✗ Lucide图标库加载失败');
            }

            // 检查必要的函数是否存在
            const requiredFunctions = [
                'renderLucideIconForSettings',
                'showIconPicker'
            ];

            requiredFunctions.forEach(funcName => {
                if (typeof window[funcName] === 'function') {
                    updateTestResults(`✓ ${funcName} 函数可用`);
                } else {
                    updateTestResults(`✗ ${funcName} 函数未找到`);
                }
            });

            // 初始化当前图标预览
            if (typeof renderLucideIconForSettings === 'function') {
                try {
                    const iconHtml = renderLucideIconForSettings('star', 20);
                    document.getElementById('current-icon-preview').innerHTML = iconHtml;
                    updateTestResults('✓ 图标预览初始化成功');
                } catch (error) {
                    updateTestResults(`✗ 图标预览初始化失败: ${error.message}`);
                }
            }
        });
    </script>
</body>
</html>
